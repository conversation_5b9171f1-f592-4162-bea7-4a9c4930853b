@props(['comment', 'post'])

<div class="comment-item py-4 px-4 hover:bg-gray-700/30 transition-colors duration-150" data-comment-id="{{ $comment->id }}">
    <div class="flex space-x-3">
        <a href="{{ route('profile.user', $comment->user) }}" class="flex-shrink-0">
            <img class="h-10 w-10 rounded-full ring-1 ring-gray-500 shadow-sm"
                 src="{{ $comment->user->avatar ? \Illuminate\Support\Facades\Storage::disk('public')->url($comment->user->avatar) : 'https://ui-avatars.com/api/?name=' . urlencode($comment->user->name) . '&color=7BC74D&background=EEEEEE' }}"
                 alt="{{ $comment->user->name }}">
        </a>
        <div class="flex-1 min-w-0">
            <div class="bg-gray-700 rounded-xl p-4 shadow-sm border border-gray-600">
                <div class="flex items-center space-x-2 mb-2">
                    <a href="{{ route('profile.user', $comment->user) }}" class="font-semibold text-white hover:text-custom-green text-sm hover:underline">
                        {{ $comment->user->name }}
                    </a>
                    <span class="text-xs text-gray-400">{{ $comment->created_at->diffForHumans() }}</span>
                    @if(auth()->check() && (auth()->id() === $comment->user_id || auth()->user()->isAdmin()))
                        <div class="relative ml-auto">
                            <button onclick="alert('Button clicked for comment {{ $comment->id }}'); toggleCommentDropdown({{ $comment->id }})" class="text-gray-400 hover:text-gray-200 p-1 rounded-full hover:bg-gray-600">
                                <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M10 6a2 2 0 110-4 2 2 0 010 4zM10 12a2 2 0 110-4 2 2 0 010 4zM10 18a2 2 0 110-4 2 2 0 010 4z"/>
                                </svg>
                            </button>
                            <div id="comment-dropdown-{{ $comment->id }}" class="hidden absolute right-0 top-8 w-36 bg-gray-600 rounded-lg shadow-lg border border-gray-500 py-1 z-50">
                                <button onclick="editComment({{ $comment->id }}); hideCommentDropdown({{ $comment->id }})"
                                        class="w-full text-left px-4 py-2 text-sm text-gray-200 hover:bg-gray-500 flex items-center space-x-2 transition-colors">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                                    </svg>
                                    <span>Edit</span>
                                </button>
                                <button onclick="deleteComment({{ $comment->id }}); hideCommentDropdown({{ $comment->id }})"
                                        class="w-full text-left px-4 py-2 text-sm text-red-400 hover:bg-red-900/20 flex items-center space-x-2 transition-colors">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                    </svg>
                                    <span>Delete</span>
                                </button>
                            </div>
                        </div>
                    @endif
                </div>

                <div class="comment-content">
                    <p class="text-gray-200 text-sm leading-relaxed">{!! nl2br(e($comment->content)) !!}</p>
                </div>

                <!-- Edit form (hidden by default) -->
                <div class="comment-edit-form hidden mt-3">
                    <form class="edit-comment-form" data-comment-id="{{ $comment->id }}">
                        @csrf
                        @method('PUT')
                        <textarea name="content" rows="2"
                                  class="w-full bg-gray-600 text-white border border-gray-500 rounded-lg shadow-sm focus:ring-custom-green focus:border-custom-green resize-none text-sm p-3">{{ $comment->content }}</textarea>
                        <div class="mt-2 flex justify-end space-x-2">
                            <button type="button" onclick="cancelEditComment({{ $comment->id }})"
                                    class="px-3 py-1.5 text-sm text-gray-400 hover:text-gray-200 rounded-md hover:bg-gray-600 transition-colors">Cancel</button>
                            <button type="submit"
                                    class="px-3 py-1.5 text-sm bg-custom-green text-white rounded-md hover:bg-green-600 transition-colors">Save</button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Comment Actions -->
            <div class="comment-actions flex items-center space-x-4 mt-2 ml-2 text-sm">
                <button onclick="toggleCommentLike({{ $comment->id }})"
                        class="flex items-center space-x-1 text-gray-500 hover:text-red-600 transition-colors group {{ $comment->isLikedBy(auth()->user()) ? 'text-red-600' : '' }}"
                        id="comment-like-btn-{{ $comment->id }}">
                    @php
                        $isLiked = $comment->isLikedBy(auth()->user());
                    @endphp
                    <svg class="w-4 h-4 {{ $isLiked ? 'text-red-600 fill-current' : '' }} group-hover:scale-110 transition-transform duration-200"
                         fill="{{ $isLiked ? 'currentColor' : 'none' }}"
                         stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                    </svg>
                    <span id="comment-like-count-{{ $comment->id }}" class="font-medium">{{ $comment->likes->count() }}</span>
                </button>

                <button onclick="showReplyForm({{ $comment->id }})" class="flex items-center space-x-1 text-gray-400 hover:text-blue-400 transition-colors group">
                    <svg class="w-4 h-4 group-hover:scale-110 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h10a8 8 0 018 8v2M3 10l6 6m-6-6l6-6" />
                    </svg>
                    <span class="font-medium">Reply</span>
                </button>
            </div>

            <!-- Reply Form (hidden by default) -->
            <div class="reply-form hidden mt-3 ml-4" id="reply-form-{{ $comment->id }}">
                @auth
                    <form class="comment-form" data-post-id="{{ $post->id }}" data-parent-id="{{ $comment->id }}">
                        @csrf
                        <div class="flex space-x-3">
                            <div class="flex-shrink-0">
                                <img class="h-8 w-8 rounded-full ring-1 ring-gray-500 shadow-sm"
                                     src="{{ auth()->user()->avatar ? \Illuminate\Support\Facades\Storage::disk('public')->url(auth()->user()->avatar) : 'https://ui-avatars.com/api/?name=' . urlencode(auth()->user()->name) . '&color=7BC74D&background=EEEEEE' }}"
                                     alt="{{ auth()->user()->name }}">
                            </div>
                            <div class="flex-1 min-w-0">
                                <div class="relative">
                                    <textarea name="content" rows="1"
                                              placeholder="Write a reply..."
                                              class="w-full px-3 py-2 bg-gray-600 text-white border border-gray-500 rounded-lg shadow-sm focus:ring-2 focus:ring-custom-green/20 focus:border-custom-green resize-none text-sm hover:bg-gray-500 transition-colors duration-200"
                                              required></textarea>
                                </div>
                                <div class="mt-2 flex justify-end space-x-2">
                                    <button type="button" onclick="hideReplyForm({{ $comment->id }})"
                                            class="px-3 py-1.5 text-sm text-gray-400 hover:text-gray-200 rounded-md hover:bg-gray-600 transition-colors">Cancel</button>
                                    <button type="submit"
                                            class="px-3 py-1.5 bg-custom-green text-white text-sm font-medium rounded-md hover:bg-custom-second-darkest shadow-sm transition-all duration-200 hover:shadow">
                                        Reply
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                @endauth
            </div>

            <!-- Replies Section -->
            @if($comment->replies && $comment->replies->count() > 0)
                <div class="mt-3">
                    <!-- View Replies Button -->
                    <button onclick="toggleModalReplies({{ $comment->id }})"
                            class="flex items-center space-x-2 text-gray-400 hover:text-gray-200 text-sm font-medium transition-colors ml-2"
                            id="modal-view-replies-btn-{{ $comment->id }}">
                        <svg class="w-4 h-4 transform transition-transform" id="modal-replies-arrow-{{ $comment->id }}" viewBox="0 0 24 24">
                            <path fill="currentColor" d="M7.41 8.59L12 13.17l4.59-4.58L18 10l-6 6-6-6 1.41-1.41z"/>
                        </svg>
                        <span id="modal-replies-text-{{ $comment->id }}">
                            View {{ $comment->replies->count() }} {{ $comment->replies->count() === 1 ? 'reply' : 'replies' }}
                        </span>
                    </button>

                    <!-- Replies List (hidden by default) -->
                    <div class="hidden mt-3 ml-4 space-y-3 border-l-2 border-gray-600 pl-4" id="modal-replies-list-{{ $comment->id }}">
                        @foreach($comment->replies as $reply)
                            <x-modal-comment-item :comment="$reply" :post="$post" />
                        @endforeach
                    </div>
                </div>
            @endif
        </div>
    </div>
</div>
